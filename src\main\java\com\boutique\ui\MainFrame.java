package com.boutique.ui;

import com.boutique.model.Utilisateur;
import com.boutique.ui.panels.*;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import java.awt.*;
import java.awt.event.*;
import java.awt.geom.Ellipse2D;
import java.awt.image.BufferedImage;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.*;


public class Main<PERSON>rame extends J<PERSON>rame {
    // ICON PATHS
    private static final String ICON_HOME        = "/icons/dashboard.png";
    private static final String ICON_PRODUCT     = "/icons/product.png";
    private static final String ICON_CLIENT      = "/icons/clients.png";
    private static final String ICON_ORDER       = "/icons/commandes.png";
    private static final String ICON_SUPPLIER    = "/icons/suppliers.png";
    private static final String ICON_USERS       = "/icons/users.png";
    private static final String ICON_ANALYTICS   = "/icons/analytics.png";
    private static final String ICON_SETTINGS    = "/icons/parametres.png";
    private static final String ICON_LOGOUT      = "/icons/logout.png";
    private static final String ICON_USER_AVATAR = "/icons/admin.png";
    private static final String ICON_LOGO        = "/icons/logo.png";

    // COLORS & FONTS
    private static final Color SIDEBAR_BG       = new Color(40, 44, 52);      // Dark gray-blue
    private static final Color SIDEBAR_ITEM_BG  = new Color(50, 54, 63);      // Slightly lighter for hover
    private static final Color SELECTED_BG      = new Color(70, 130, 180);    // Soft steel blue
    private static final Color CONTENT_BG       = new Color(250, 250, 250);   // Near-white for content
    private static final Color TEXT_LIGHT       = new Color(245, 245, 245);   // Light gray text
    private static final Color TEXT_SECONDARY   = new Color(160, 170, 180);   // Softer muted gray
    private static final Color BADGE_BG         = new Color(100, 149, 237);   // Cornflower blue for badges


    private static final Font FONT_TITLE     = new Font("Segoe UI", Font.BOLD, 18);
    private static final Font FONT_MENU_ITEM = new Font("Segoe UI", Font.PLAIN, 14);
    private static final Font FONT_USER_NAME = new Font("Segoe UI", Font.BOLD, 14);
    private static final Font FONT_USER_ROLE = new Font("Segoe UI", Font.PLAIN, 12);
    private static final Font FONT_SECTION   = new Font("Segoe UI", Font.BOLD, 11);
    private static final Font FONT_BADGE     = new Font("Segoe UI", Font.BOLD, 11);

    private final CardLayout cardLayout = new CardLayout();
    private final JPanel      cards      = new JPanel(cardLayout);
    private final Map<String,SidebarMenuItem> menuButtons = new LinkedHashMap<>();

    public MainFrame(Utilisateur user) {
        super("Gestion Boutique");
        setDefaultCloseOperation(EXIT_ON_CLOSE);
        setSize(1200, 800);
        setLocationRelativeTo(null);
        setLayout(new BorderLayout());

        // Application icon
        setIconImage(loadImage(ICON_LOGO, 32, 32));

        // Sidebar (role-aware)
        JPanel sidebar = createSidebar(user);
        add(sidebar, BorderLayout.WEST);

        // Content area (only panels user can access)
        setupContentPanels(user);
        cards.setBackground(CONTENT_BG);
        add(cards, BorderLayout.CENTER);

        // Start on dashboard
        selectMenuItem("Tableau de Bord");

        setVisible(true);
    }

    private JPanel createSidebar(Utilisateur user) {
        JPanel sidebar = new JPanel();
        sidebar.setLayout(new BoxLayout(sidebar, BoxLayout.Y_AXIS));
        sidebar.setBackground(SIDEBAR_BG);
        sidebar.setPreferredSize(new Dimension(250, 0));

        // Logo + title
        JPanel logoPanel = new JPanel();
        logoPanel.setBackground(SIDEBAR_BG);
        logoPanel.setBorder(new EmptyBorder(20, 0, 20, 0));
        logoPanel.setLayout(new BoxLayout(logoPanel, BoxLayout.Y_AXIS));
        logoPanel.setAlignmentX(Component.CENTER_ALIGNMENT);
        logoPanel.add(new JLabel(loadIcon(ICON_LOGO, 40, 40)) {{
            setAlignmentX(CENTER_ALIGNMENT);
        }});
        logoPanel.add(Box.createVerticalStrut(10));
        logoPanel.add(new JLabel("Gestion Boutique") {{
            setFont(FONT_TITLE);
            setForeground(TEXT_LIGHT);
            setAlignmentX(CENTER_ALIGNMENT);
        }});
        sidebar.add(logoPanel);

        // User profile
        JPanel userPanel = new JPanel(new BorderLayout(10,0));
        userPanel.setBackground(SIDEBAR_ITEM_BG);
        userPanel.setBorder(new EmptyBorder(10,10,10,10));
        userPanel.setMaximumSize(new Dimension(250, 80));
        userPanel.add(new JLabel(createCircularAvatar(ICON_USER_AVATAR, 40)), BorderLayout.WEST);
        userPanel.add(new JPanel() {{
            setOpaque(false);
            setLayout(new BoxLayout(this, BoxLayout.Y_AXIS));
            add(new JLabel(user.getNomUtilisateur()) {{
                setFont(FONT_USER_NAME);
                setForeground(TEXT_LIGHT);
            }});
            add(Box.createVerticalStrut(5));
            add(new JLabel(user.getRole()) {{
                setFont(FONT_USER_ROLE);
                setForeground(TEXT_SECONDARY);
            }});
        }}, BorderLayout.CENTER);
        sidebar.add(userPanel);
        sidebar.add(Box.createVerticalStrut(20));

        // NAVIGATION
        sidebar.add(new JLabel("NAVIGATION") {{
            setFont(FONT_SECTION);
            setForeground(TEXT_SECONDARY);
            setBorder(new EmptyBorder(0,12,5,0));
            setAlignmentX(LEFT_ALIGNMENT);
        }});

        // Always available
        addMenuItem(sidebar, "Tableau de Bord", ICON_HOME, null);
        addMenuItem(sidebar, "Produits",        ICON_PRODUCT, "");
        addMenuItem(sidebar, "Clients",         ICON_CLIENT,  "");
        addMenuItem(sidebar, "Commandes",       ICON_ORDER,   "");

        // Admin-only
        if (user.isAdmin()) {
            addMenuItem(sidebar, "Fournisseurs", ICON_SUPPLIER, null);
            addMenuItem(sidebar, "Utilisateurs", ICON_USERS,    null);
        }

        sidebar.add(Box.createVerticalStrut(20));

        // OUTILS
        sidebar.add(new JLabel("OUTILS") {{
            setFont(FONT_SECTION);
            setForeground(TEXT_SECONDARY);
            setBorder(new EmptyBorder(0,12,5,0));
            setAlignmentX(LEFT_ALIGNMENT);
        }});
        if (user.isAdmin()) {
            addMenuItem(sidebar, "Analytiques", ICON_ANALYTICS, null);
        }
        addMenuItem(sidebar, "Paramètres",  ICON_SETTINGS,  null);


        sidebar.add(Box.createVerticalGlue());

        // Logout
        sidebar.add(new JSeparator() {{
            setForeground(TEXT_SECONDARY);
            setMaximumSize(new Dimension(220,1));
            setAlignmentX(CENTER_ALIGNMENT);
        }});
        sidebar.add(Box.createVerticalStrut(10));
        addMenuItem(sidebar, "Déconnexion", ICON_LOGOUT, null, e -> handleLogout());

        return sidebar;
    }

    private void addMenuItem(JPanel sidebar, String text, String iconPath, String badge) {
        addMenuItem(sidebar, text, iconPath, badge, null);
    }

    private void addMenuItem(JPanel sidebar, String text, String iconPath, String badge, ActionListener extra) {
        SidebarMenuItem item = new SidebarMenuItem(text, iconPath, badge);
        item.addActionListener(e -> selectMenuItem(text));
        if (extra != null) item.addActionListener(extra);
        menuButtons.put(text, item);
        sidebar.add(item);
        sidebar.add(Box.createVerticalStrut(5));
    }

    private void setupContentPanels(Utilisateur user) {
        Map<String,JPanel> panels = new LinkedHashMap<>();
        panels.put("Tableau de Bord", new WelcomePanel(user));
        panels.put("Produits",        new ProduitPanel(user));
        panels.put("Clients",         new ClientPanel(user));
        panels.put("Commandes",       new CommandePanel(user));
        panels.put("Paramètres",   new SettingsPanel(user));
        if (user.isAdmin()) {
            panels.put("Fournisseurs", new FournisseurPanel(user));
            panels.put("Utilisateurs", new UtilisateurPanel(user));
            panels.put("Analytiques",  new GestionStockPanel(user));

        }
        panels.forEach((name,panel)->{
            panel.setBackground(CONTENT_BG);
            panel.setBorder(new EmptyBorder(15,15,15,15));
            cards.add(panel, name);
        });
        add(cards, BorderLayout.CENTER);
    }

    private void selectMenuItem(String key) {
        cardLayout.show(cards, key);
        menuButtons.forEach((k,btn)-> btn.setSelected(k.equals(key)));
    }

    private void handleLogout() {
        if (JOptionPane.showConfirmDialog(
                this, "Déconnecter ?", "Confirmation", JOptionPane.YES_NO_OPTION
        ) == JOptionPane.YES_OPTION) {
            new LoginFrame().setVisible(true);
            dispose();
        }
    }

    // Utility methods

    private Image loadImage(String path, int w, int h) {
        URL u = getClass().getResource(path);
        if (u == null) return null;
        return new ImageIcon(u).getImage()
                .getScaledInstance(w, h, Image.SCALE_SMOOTH);
    }

    private Icon loadIcon(String path, int w, int h) {
        Image img = loadImage(path, w, h);
        return img != null
                ? new ImageIcon(img)
                : UIManager.getIcon("OptionPane.warningIcon");
    }

    private Icon createCircularAvatar(String path, int size) {
        Icon ic = loadIcon(path, size, size);
        if (!(ic instanceof ImageIcon)) return ic;
        Image img = ((ImageIcon) ic).getImage();
        BufferedImage buf = new BufferedImage(size, size, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g = buf.createGraphics();
        g.setClip(new Ellipse2D.Float(0,0,size,size));
        g.drawImage(img,0,0,null);
        g.dispose();
        return new ImageIcon(buf);
    }

    /**
     * A single, reusable sidebar‐item component.
     */
    private class SidebarMenuItem extends JPanel {
        private final JLabel iconLbl, textLbl;
        private final String badge;
        private boolean selected = false, hovered = false;
        private final java.util.List<ActionListener> listeners = new ArrayList<>();

        SidebarMenuItem(String text, String iconPath, String badge) {
            this.badge = badge;
            setLayout(new BorderLayout(8,0));
            setBackground(SIDEBAR_BG);
            setBorder(new EmptyBorder(8,12,8,12));
            setMaximumSize(new Dimension(250,40));
            setCursor(Cursor.getPredefinedCursor(Cursor.HAND_CURSOR));

            iconLbl = new JLabel(loadIcon(iconPath,20,20));
            textLbl = new JLabel(text);
            textLbl.setFont(FONT_MENU_ITEM);
            textLbl.setForeground(TEXT_LIGHT);

            add(iconLbl, BorderLayout.WEST);
            add(textLbl, BorderLayout.CENTER);

            addMouseListener(new MouseAdapter() {
                public void mouseEntered(MouseEvent e) { hovered = true; updateBg(); }
                public void mouseExited(MouseEvent e)  { hovered = false; updateBg(); }
                public void mouseClicked(MouseEvent e) {
                    for (ActionListener l : listeners) {
                        l.actionPerformed(new ActionEvent(SidebarMenuItem.this, 0, "click"));
                    }
                }
            });
        }

        void addActionListener(ActionListener l) {
            listeners.add(l);
        }

        void setSelected(boolean sel) {
            this.selected = sel;
            updateBg();
        }

        private void updateBg() {
            if (selected) {
                setBackground(SELECTED_BG);
            } else if (hovered) {
                setBackground(SIDEBAR_ITEM_BG);
            } else {
                setBackground(SIDEBAR_BG);
            }
            repaint();
        }

        protected void paintComponent(Graphics g) {
            super.paintComponent(g);
            if (badge != null) {
                Graphics2D g2 = (Graphics2D) g.create();
                g2.setFont(FONT_BADGE);
                FontMetrics fm = g2.getFontMetrics();
                int w = fm.stringWidth(badge) + 8;
                int h = 16;
                int x = getWidth() - w - 8;
                int y = (getHeight() - h) / 2;
                g2.setColor(BADGE_BG);
                g2.fillRoundRect(x, y, w, h, 8, 8);
                g2.setColor(TEXT_LIGHT);
                g2.drawString(badge, x + 4, y + h - 4);
                g2.dispose();
            }
        }
    }

}