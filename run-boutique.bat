@echo off
echo Starting Boutique Management Application...
echo.
echo Make sure Laragon is running with MySQL started!
echo.

REM Create lib directory if it doesn't exist
if not exist "lib" mkdir lib

REM Check if dependencies exist
if not exist "lib\mysql-connector-java-8.0.33.jar" (
    echo Missing MySQL Connector! Please download it manually.
    echo Download from: https://dev.mysql.com/downloads/connector/j/
    echo Place mysql-connector-java-8.0.33.jar in the lib folder
    pause
    exit /b 1
)

REM Compile the application first
echo Compiling application...
javac -cp "lib\*" -d target\classes src\main\java\com\boutique\**\*.java

REM Run the application with all dependencies
echo Running application...
java -cp "target\classes;lib\*" com.boutique.ui.LoginFrame

pause
