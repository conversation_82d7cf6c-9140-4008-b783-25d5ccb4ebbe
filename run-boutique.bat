@echo off
echo Starting Boutique Management Application...
echo.
echo Make sure Laragon is running with MySQL started!
echo.

REM Create lib directory if it doesn't exist
if not exist "lib" mkdir lib

REM Check if MySQL connector exists (either old or new naming)
set MYSQL_JAR_FOUND=0
if exist "lib\mysql-connector-j-8.0.33.jar" set MYSQL_JAR_FOUND=1
if exist "lib\mysql-connector-java-8.0.33.jar" set MYSQL_JAR_FOUND=1

if %MYSQL_JAR_FOUND%==0 (
    echo Missing MySQL Connector! Please download it manually.
    echo Download from: https://dev.mysql.com/downloads/connector/j/
    echo Place mysql-connector-j-8.0.33.jar in the lib folder
    pause
    exit /b 1
)

echo Compiling application (excluding PDF functionality)...
if not exist "target\classes" mkdir target\classes

REM Compile in correct order to handle dependencies
echo Compiling models...
javac -cp "lib\*" -d target\classes src\main\java\com\boutique\model\*.java

echo Compiling DAOs...
javac -cp "lib\*;target\classes" -d target\classes src\main\java\com\boutique\dao\*.java
javac -cp "lib\*;target\classes" -d target\classes src\main\java\com\boutique\dao\impl\*.java

echo Compiling utilities (excluding PDFGenerator)...
javac -cp "lib\*;target\classes" -d target\classes src\main\java\com\boutique\util\DBConnection.java
javac -cp "lib\*;target\classes" -d target\classes src\main\java\com\boutique\util\DatabaseInitializer.java
javac -cp "lib\*;target\classes" -d target\classes src\main\java\com\boutique\util\SessionManager.java
javac -cp "lib\*;target\classes" -d target\classes src\main\java\com\boutique\util\StockAlertService.java

echo Compiling UI components...
javac -cp "lib\*;target\classes" -d target\classes src\main\java\com\boutique\ui\*.java
javac -cp "lib\*;target\classes" -d target\classes src\main\java\com\boutique\ui\panels\*.java

echo Starting application...
java -cp "target\classes;lib\*" com.boutique.ui.LoginFrame

pause
