@echo off
echo Starting Boutique Management Application...
echo.
echo Make sure Laragon is running with MySQL started!
echo.

REM Create lib directory if it doesn't exist
if not exist "lib" mkdir lib

REM Check if dependencies exist
if not exist "lib\mysql-connector-j-8.0.33.jar" (
    echo Missing MySQL Connector! Please download it manually.
    echo Download from: https://dev.mysql.com/downloads/connector/j/
    echo Place mysql-connector-j-8.0.33.jar in the lib folder
    pause
    exit /b 1
)

REM Use pre-compiled JAR if available, otherwise try to run directly
echo Running application...
if exist "target\boutique-stock-order-1.0-SNAPSHOT.jar" (
    java -cp "target\boutique-stock-order-1.0-SNAPSHOT.jar;lib\*" com.boutique.ui.LoginFrame
) else (
    echo Compiling essential classes...
    if not exist "target\classes" mkdir target\classes
    javac -cp "lib\*" -d target\classes src\main\java\com\boutique\model\*.java
    javac -cp "lib\*;target\classes" -d target\classes src\main\java\com\boutique\dao\*.java
    javac -cp "lib\*;target\classes" -d target\classes src\main\java\com\boutique\dao\impl\*.java
    javac -cp "lib\*;target\classes" -d target\classes src\main\java\com\boutique\util\DBConnection.java
    javac -cp "lib\*;target\classes" -d target\classes src\main\java\com\boutique\util\DatabaseInitializer.java
    javac -cp "lib\*;target\classes" -d target\classes src\main\java\com\boutique\util\SessionManager.java
    javac -cp "lib\*;target\classes" -d target\classes src\main\java\com\boutique\ui\*.java
    javac -cp "lib\*;target\classes" -d target\classes src\main\java\com\boutique\ui\panels\*.java
    java -cp "target\classes;lib\*" com.boutique.ui.LoginFrame
)

pause
