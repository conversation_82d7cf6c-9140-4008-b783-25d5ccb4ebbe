// src/main/java/com/boutique/model/Cmd_prd_pivot.java
package com.boutique.model;

public class Cmd_prd_pivot {
    private Produit produit;
    private int     quantite;

    public Cmd_prd_pivot(Produit produit, int quantite) {
        this.produit  = produit;
        this.quantite = quantite;
    }

    public Produit getProduit() { return produit; }
    public int     getQuantite() { return quantite; }
    public double  getPrixUnitaire() { return produit.getPrix(); }
    public double  getSousTotal()    { return quantite * getPrixUnitaire(); }

    public void setProduit(Produit v) {
        this.produit = v;
    }

    public void setQuantite(Integer v) {
        if (v != null) {
            this.quantite = v;
        } else {
            this.quantite = 0;
        }
    }
}
