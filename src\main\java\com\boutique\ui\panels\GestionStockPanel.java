// src/main/java/com/boutique/ui/panels/GestionStockPanel.java
package com.boutique.ui.panels;

import com.boutique.dao.impl.FournisseurDAOImpl;
import com.boutique.dao.impl.ProduitDAOImpl;
import com.boutique.model.Fournisseur;
import com.boutique.model.Produit;
import com.boutique.model.Utilisateur;
import com.boutique.util.StockAlertService;

import javax.swing.*;
import javax.swing.border.TitledBorder;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.io.File;
import java.util.List;
import java.util.stream.Collectors;

public class GestionStockPanel extends JPanel {
    private final ProduitDAOImpl produitDao        = new ProduitDAOImpl();
    private final FournisseurDAOImpl fournisseurDao = new FournisseurDAOImpl();

    // fonts
    private final Font labelFont = new Font("Segoe UI", Font.BOLD, 14);
    private final Font fieldFont = new Font("Segoe UI", Font.PLAIN, 14);

    // --- top search bar ---
    private final JTextField tfSearch = new JTextField(20);
    private final JButton   btnSearch = new JButton("Rechercher");

    // --- alert threshold control ---
    private final JSpinner spinnerThreshold = new JSpinner(new SpinnerNumberModel(10, 0, Integer.MAX_VALUE, 1));
    private final JButton  btnSetThreshold  = new JButton("Définir alerte");

    // --- cards ---
    private final CardLayout cardLayout    = new CardLayout();
    private final JPanel     contentPanel  = new JPanel(cardLayout);
    private static final String CARD_PLACE = "PLACEHOLDER";
    private static final String CARD_DETAIL = "DETAIL";

    // --- detail view components ---
    private final JLabel     lblImage       = new JLabel();
    private final JLabel     lblNom         = styleLabel("");
    private final JLabel     lblDescription = new JLabel();
    private final JLabel     lblCategorie   = styleLabel("");
    private final JLabel     lblFournisseur = styleLabel("");
    private final JTextField tfStock        = new JTextField(8);
    private final JTextField tfPrix         = new JTextField(8);
    private final JButton    btnUpdate      = new JButton("Enregistrer");

    private Produit current;

    public GestionStockPanel(Utilisateur user) {
        // 1) init tray for alerts
        StockAlertService.initSystemTray();

        // 2) panel setup
        setLayout(new BorderLayout(10,10));
        setBackground(Color.WHITE);

        // 3) recherche + seuil de stocke f StockAlertService
        JPanel searchBar = new JPanel(new FlowLayout(FlowLayout.LEFT,5,5));
        searchBar.setBackground(Color.WHITE);
        searchBar.setBorder(BorderFactory.createTitledBorder(BorderFactory.createLineBorder(Color.LIGHT_GRAY), "Recherche", TitledBorder.LEFT, TitledBorder.TOP, labelFont));
        JLabel lblS = styleLabel("Produit :");
        searchBar.add(lblS);
        tfSearch.setFont(fieldFont);
        searchBar.add(tfSearch);
        btnSearch.setFont(fieldFont);
        searchBar.add(btnSearch);

        JLabel lblT = styleLabel("Seuil alerte :");
        searchBar.add(lblT);
        spinnerThreshold.setFont(fieldFont);
        searchBar.add(spinnerThreshold);
        btnSetThreshold.setFont(fieldFont);
        searchBar.add(btnSetThreshold);

        add(searchBar, BorderLayout.NORTH);


        JPanel placeholder = new JPanel(new GridBagLayout());
        placeholder.setBackground(Color.WHITE);
        JLabel lblPh = new JLabel("Gestion de stock");
        lblPh.setFont(labelFont.deriveFont(24f));
        placeholder.add(lblPh);

        // 4) detail panel
        JPanel detail = new JPanel(new GridBagLayout());
        detail.setBackground(Color.WHITE);
        detail.setBorder(BorderFactory.createTitledBorder(BorderFactory.createLineBorder(Color.LIGHT_GRAY), "Détails du produit", TitledBorder.LEFT, TitledBorder.TOP, labelFont));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(8,8,8,8);

        // --- left columns ---
        gbc.anchor = GridBagConstraints.WEST;
        gbc.fill   = GridBagConstraints.HORIZONTAL;
        gbc.weightx = 0.6;

        // Nom
        gbc.gridx=0; gbc.gridy=0;
        detail.add(styleLabel("Nom :"), gbc);
        gbc.gridx=1;
        lblNom.setFont(fieldFont);
        detail.add(lblNom, gbc);

        // Description
        gbc.gridx=0; gbc.gridy=1;
        detail.add(styleLabel("Description :"), gbc);
        gbc.gridx=1;
        lblDescription.setFont(fieldFont);
        lblDescription.setVerticalAlignment(SwingConstants.TOP);
        lblDescription.setPreferredSize(new Dimension(250,60));
        detail.add(lblDescription, gbc);

        // Catégorie
        gbc.gridx=0; gbc.gridy=2;
        detail.add(styleLabel("Catégorie :"), gbc);
        gbc.gridx=1;
        detail.add(lblCategorie, gbc);

        // Fournisseur
        gbc.gridx=0; gbc.gridy=3;
        detail.add(styleLabel("Fournisseur :"), gbc);
        gbc.gridx=1;
        detail.add(lblFournisseur, gbc);

        // Stock
        gbc.gridx=0; gbc.gridy=4;
        detail.add(styleLabel("Stock :"), gbc);
        gbc.gridx=1;
        tfStock.setFont(fieldFont);
        tfStock.setHorizontalAlignment(JTextField.RIGHT);
        detail.add(tfStock, gbc);

        // Prix
        gbc.gridx=0; gbc.gridy=5;
        detail.add(styleLabel("Prix (MAD) :"), gbc);
        gbc.gridx=1;
        tfPrix.setFont(fieldFont);
        tfPrix.setHorizontalAlignment(JTextField.RIGHT);
        detail.add(tfPrix, gbc);

        // Enregistrer
        gbc.gridx=0; gbc.gridy=6; gbc.gridwidth=2;
        gbc.anchor = GridBagConstraints.CENTER;
        btnUpdate.setFont(fieldFont);
        detail.add(btnUpdate, gbc);

        // --- image---
        lblImage.setPreferredSize(new Dimension(300,300));
        lblImage.setBorder(BorderFactory.createLineBorder(Color.LIGHT_GRAY));
        gbc.gridx=2; gbc.gridy=0; gbc.gridheight=7;
        gbc.fill = GridBagConstraints.NONE;
        gbc.anchor = GridBagConstraints.CENTER;
        gbc.weightx = 0.4;
        detail.add(lblImage, gbc);

        // 6) ajout
        contentPanel.add(placeholder,CARD_PLACE);
        contentPanel.add(detail,CARD_DETAIL);
        add(contentPanel, BorderLayout.CENTER);
        cardLayout.show(contentPanel, CARD_PLACE);

        // 7) listeners
        btnSearch.addActionListener(e -> rechercherProduit());
        tfSearch.addActionListener(e -> rechercherProduit());
        btnSetThreshold.addActionListener(e -> {
            int t = (Integer) spinnerThreshold.getValue();
            StockAlertService.setThreshold(t);
            StockAlertService.checkStock();
            JOptionPane.showMessageDialog(this, "Seuil d'alerte défini à " + t + " unité(s).", "Alerte mise à jour", JOptionPane.INFORMATION_MESSAGE
            );
        });
        btnUpdate.addActionListener(this::mettreAJourProduit);
    }

    private JLabel styleLabel(String txt) {
        JLabel l = new JLabel(txt);
        l.setFont(labelFont);
        return l;
    }

    private void rechercherProduit() {
        String term = tfSearch.getText().trim().toLowerCase();
        if (term.isEmpty()) {
            cardLayout.show(contentPanel, CARD_PLACE);
            return;
        }
        List<Produit> list = produitDao.findAll().stream()
                .filter(p -> p.getNom().toLowerCase().contains(term))
                .collect(Collectors.toList());
        if (list.isEmpty()) {
            JOptionPane.showMessageDialog(this,
                    "Aucun produit trouvé.",
                    "Info", JOptionPane.INFORMATION_MESSAGE);
            clearFields();
            cardLayout.show(contentPanel, CARD_PLACE);
        } else {
            current = list.get(0);
            afficherDetail(current);
            cardLayout.show(contentPanel, CARD_DETAIL);
        }
    }

    private void afficherDetail(Produit p) {
        lblNom.setText(p.getNom());
        lblDescription.setText("<html><body style='width:230px;'>" + p.getDescription() + "</body></html>");
        lblCategorie.setText(p.getCategorie());
        Fournisseur f = fournisseurDao.findById(p.getFournisseurId());
        lblFournisseur.setText(f!=null?f.getNom():"—");
        tfStock.setText(String.valueOf(p.getQuantiteStock()));
        tfPrix.setText(String.valueOf(p.getPrix()));
        lblImage.setIcon(null);
        if (p.getPicture()!=null) {
            File imgF = new File(p.getPicture());
            if (imgF.exists()) {
                ImageIcon ic = new ImageIcon(p.getPicture());
                Image img = ic.getImage().getScaledInstance(
                        lblImage.getWidth(), lblImage.getHeight(),
                        Image.SCALE_SMOOTH);
                lblImage.setIcon(new ImageIcon(img));
            }
        }
    }

    private void mettreAJourProduit(ActionEvent e) {
        if (current==null) return;
        try {
            current.setQuantiteStock(Integer.parseInt(tfStock.getText().trim()));
            current.setPrix(Double.parseDouble(tfPrix.getText().trim()));
            produitDao.update(current);
            StockAlertService.checkStock();
            JOptionPane.showMessageDialog(this, "Produit mis à jour avec succès.", "Succès", JOptionPane.INFORMATION_MESSAGE);
        } catch (NumberFormatException ex) {
            JOptionPane.showMessageDialog(this, "Valeur de stock/prix invalide.", "Erreur", JOptionPane.ERROR_MESSAGE);
        }
    }

    private void clearFields() {
        lblNom.setText("");
        lblDescription.setText("");
        lblCategorie.setText("");
        lblFournisseur.setText("");
        tfStock.setText("");
        tfPrix.setText("");
        lblImage.setIcon(null);
        current = null;
    }
}
