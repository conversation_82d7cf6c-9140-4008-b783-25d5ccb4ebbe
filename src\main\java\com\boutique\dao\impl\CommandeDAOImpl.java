// src/main/java/com/boutique/dao/impl/CommandeDAOImpl.java
package com.boutique.dao.impl;

import com.boutique.dao.GenericDAO;
import com.boutique.model.Cmd_prd_pivot;
import com.boutique.model.Commande;
import com.boutique.model.Produit;
import com.boutique.util.DBConnection;

import java.sql.*;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

public class CommandeDAOImpl implements GenericDAO<Commande> {

    @Override
    public void insert(Commande c) {
        // 1) insert header
        String sql = """
            INSERT INTO commande(dateCommande, client_id, created_by, statut)
            VALUES (?,?,?,?)
            """;
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement ps = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {

            ps.setDate(1, Date.valueOf(c.getDateCommande()));
            ps.setInt(2, c.getClient().getIdClient());
            ps.setString(3, c.getCreatedBy());
            ps.setString(4, c.getStatut());
            int n = ps.executeUpdate();
            if (n == 0) throw new SQLException("Aucune ligne commande insérée.");
            try (ResultSet rs = ps.getGeneratedKeys()) {
                if (rs.next()) {
                    c.setIdCommande(rs.getInt(1));
                } else {
                    throw new SQLException("Échec génération ID commande.");
                }
            }
        } catch (SQLException ex) {
            throw new RuntimeException("Erreur insert commande", ex);
        }

        // 2) insert pivot lines
        String sqlPivot = """
            INSERT INTO commande_produit(commande_id, produit_id, quantite)
            VALUES (?,?,?)
            """;
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement ps2 = conn.prepareStatement(sqlPivot)) {

            for (Cmd_prd_pivot line : c.getLignesCommande()) {
                ps2.setInt(1, c.getIdCommande());
                ps2.setInt(2, line.getProduit().getIdProduit());
                ps2.setInt(3, line.getQuantite());
                ps2.addBatch();
            }
            ps2.executeBatch();
        } catch (SQLException ex) {
            throw new RuntimeException("Erreur insert lignes commande", ex);
        }
    }

    @Override
    public void update(Commande c) {
        // 1) Mise à jour de l'en-tête
        String sqlHeader = """
            UPDATE commande
               SET dateCommande = ?,
                   client_id     = ?,
                   created_by    = ?,
                   statut        = ?
             WHERE idCommande   = ?
            """;
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement ps = conn.prepareStatement(sqlHeader)) {
            ps.setDate(1, Date.valueOf(c.getDateCommande()));
            ps.setInt(2, c.getClient().getIdClient());
            ps.setString(3, c.getCreatedBy());
            ps.setString(4, c.getStatut());
            ps.setInt(5, c.getIdCommande());
            ps.executeUpdate();
        } catch (SQLException ex) {
            throw new RuntimeException("Erreur lors de la mise à jour de l'en-tête de commande", ex);
        }

        // 2) Suppression des anciennes lignes de pivot
        String sqlDeleteLines = "DELETE FROM commande_produit WHERE commande_id = ?";
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement ps = conn.prepareStatement(sqlDeleteLines)) {
            ps.setInt(1, c.getIdCommande());
            ps.executeUpdate();
        } catch (SQLException ex) {
            throw new RuntimeException("Erreur lors de la suppression des anciennes lignes de commande", ex);
        }

        // 3) Ré-insertion des lignes pivot uniquement
        String sqlInsertLine = """
            INSERT INTO commande_produit(commande_id, produit_id, quantite)
            VALUES (?, ?, ?)
            """;
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement ps = conn.prepareStatement(sqlInsertLine)) {
            for (Cmd_prd_pivot line : c.getLignesCommande()) {
                ps.setInt(1, c.getIdCommande());
                ps.setInt(2, line.getProduit().getIdProduit());
                ps.setInt(3, line.getQuantite());
                ps.addBatch();
            }
            ps.executeBatch();
        } catch (SQLException ex) {
            throw new RuntimeException("Erreur lors de l'insertion des lignes de commande", ex);
        }
    }

    @Override
    public void delete(int id) {
        // cascade on commande_produit due to FK
        String sql = "DELETE FROM commande WHERE idCommande = ?";
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement ps = conn.prepareStatement(sql)) {
            ps.setInt(1, id);
            ps.executeUpdate();
        } catch (SQLException ex) {
            throw new RuntimeException("Erreur delete commande", ex);
        }
    }

    @Override
    public Commande findById(int id) {
        String sql = """
            SELECT dateCommande, client_id, created_by, statut
              FROM commande
             WHERE idCommande = ?
            """;
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement ps = conn.prepareStatement(sql)) {

            ps.setInt(1, id);
            try (ResultSet rs = ps.executeQuery()) {
                if (!rs.next()) return null;
                Commande c = new Commande();
                c.setIdCommande(id);
                c.setDateCommande(rs.getDate("dateCommande").toLocalDate());
                c.setClient(new ClientDAOImpl().findById(rs.getInt("client_id")));
                c.setCreatedBy(rs.getString("created_by"));
                c.setStatut(rs.getString("statut"));
                c.setLignesCommande(loadLignes(id));
                return c;
            }
        } catch (SQLException ex) {
            throw new RuntimeException("Erreur findById commande", ex);
        }
    }

    @Override
    public List<Commande> findAll() {
        String sql = """
            SELECT idCommande, dateCommande, client_id, created_by, statut
              FROM commande
             ORDER BY dateCommande DESC
            """;
        List<Commande> list = new ArrayList<>();
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement ps = conn.prepareStatement(sql);
             ResultSet rs = ps.executeQuery()) {

            while (rs.next()) {
                Commande c = new Commande();
                int id = rs.getInt("idCommande");
                c.setIdCommande(id);
                c.setDateCommande(rs.getDate("dateCommande").toLocalDate());
                c.setClient(new ClientDAOImpl().findById(rs.getInt("client_id")));
                c.setCreatedBy(rs.getString("created_by"));
                c.setStatut(rs.getString("statut"));
                c.setLignesCommande(loadLignes(id));
                list.add(c);
            }
        } catch (SQLException ex) {
            throw new RuntimeException("Erreur findAll commande", ex);
        }
        return list;
    }

    // --- Helper to load pivot lines ---
    private List<Cmd_prd_pivot> loadLignes(int commandeId) {
        String sql = """
            SELECT cp.produit_id, cp.quantite,
                   p.nom, p.description, p.prix,
                   p.quantiteStock, p.categorie, p.fournisseurId, p.picture
              FROM commande_produit cp
              JOIN produit p ON p.idProduit = cp.produit_id
             WHERE cp.commande_id = ?
            """;
        List<Cmd_prd_pivot> lines = new ArrayList<>();
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement ps = conn.prepareStatement(sql)) {
            ps.setInt(1, commandeId);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    Produit p = new Produit(
                            rs.getInt("produit_id"),
                            rs.getString("nom"),
                            rs.getString("description"),
                            rs.getDouble("prix"),
                            rs.getInt("quantiteStock"),
                            rs.getString("categorie"),
                            rs.getInt("fournisseurId"),
                            rs.getString("picture")
                    );
                    lines.add(new Cmd_prd_pivot(p, rs.getInt("quantite")));
                }
            }
        } catch (SQLException ex) {
            throw new RuntimeException("Erreur load lignes commande", ex);
        }
        return lines;
    }
}
