// src/main/java/com/boutique/model/Commande.java
package com.boutique.model;

import java.time.LocalDate;
import java.util.List;

public class Commande {
    private int idCommande;
    private LocalDate dateCommande;
    private Client client;
    private List<Cmd_prd_pivot> lignesCommande;
    private String createdBy;
    // Un seul String pour le statut
    // Valeurs possibles : "en attente", "expédiée", "annulée"
    private String statut = "en attente";

    public Commande() { }

    public Commande(int id, LocalDate date, Client client,
                    List<Cmd_prd_pivot> lignes, String statut) {
        this.idCommande     = id;
        this.dateCommande   = date;
        this.client         = client;
        this.lignesCommande = lignes;
        this.statut         = statut;
    }

    public int getIdCommande() {
        return idCommande;
    }
    public void setIdCommande(int id) {
        this.idCommande = id;
    }

    public LocalDate getDateCommande() {
        return dateCommande;
    }
    public void setDateCommande(LocalDate date) {
        this.dateCommande = date;
    }

    public Client getClient() {
        return client;
    }
    public void setClient(Client client) {
        this.client = client;
    }

    public List<Cmd_prd_pivot> getLignesCommande() {
        return lignesCommande;
    }
    public void setLignesCommande(List<Cmd_prd_pivot> lignes) {
        this.lignesCommande = lignes;
    }

    public double getTotal() {
        if (lignesCommande == null) return 0.0;
        return lignesCommande.stream()
                .mapToDouble(l -> l.getProduit().getPrix() * l.getQuantite())
                .sum();
    }

    public String getCreatedBy() {
        return createdBy;
    }
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getStatut() {
        return statut;
    }
    public void setStatut(String statut) {
        this.statut = statut;
    }
}
