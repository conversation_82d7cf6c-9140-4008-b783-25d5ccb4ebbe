package com.boutique.ui.panels;

import com.boutique.dao.impl.ClientDAOImpl;
import com.boutique.model.Client;
import com.boutique.model.Utilisateur;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.table.*;
import javax.swing.RowFilter;
import java.awt.*;
import java.util.List;

public class ClientPanel extends JPanel {

    private final ClientDAOImpl dao = new ClientDAOImpl();
    private final DefaultTableModel model = new DefaultTableModel(
            new String[]{"ID", "Nom", "Prénom", "Adresse", "Téléphone", "Email"}, 0
    );
    private final JTable table = new JTable(model);
    private final TableRowSorter<DefaultTableModel> sorter =
            new TableRowSorter<>(model);

    // Search components
    private final JTextField tfSearch = new JTextField(20);
    private final JButton btnSearch   = new JButton("Rechercher");
    private final JButton btnClear    = new JButton("Effacer");

    public ClientPanel(Utilisateur user) {
        setBackground(Color.WHITE);
        setBorder(new EmptyBorder(15,15,15,15));
        setLayout(new BorderLayout(5,5));

        // --- Search bar ---
        JPanel searchBar = new JPanel(new FlowLayout(FlowLayout.LEFT));
        searchBar.add(new JLabel("Recherche client:"));
        searchBar.add(tfSearch);
        searchBar.add(btnSearch);
        searchBar.add(btnClear);
        add(searchBar, BorderLayout.NORTH);

        // --- Table setup ---
        table.setFillsViewportHeight(true);
        table.setRowHeight(24);
        table.setShowGrid(false);
        table.setSelectionBackground(new Color(93,173,226));
        table.setSelectionForeground(Color.WHITE);
        table.setRowSorter(sorter);
        add(new JScrollPane(table), BorderLayout.CENTER);

        // --- Control buttons ---
        JPanel ctrl = new JPanel();
        JButton btnAdd = new JButton("Ajouter");
        JButton btnEdit = new JButton("Modifier");
        JButton btnDel = new JButton("Supprimer");
        JButton btnRef = new JButton("Actualiser");
        ctrl.add(btnAdd);
        ctrl.add(btnEdit);
        ctrl.add(btnDel);
        ctrl.add(btnRef);
        add(ctrl, BorderLayout.SOUTH);

        // --- Wire search ---
        btnSearch.addActionListener(e -> {
            String text = tfSearch.getText().trim();
            if (text.isEmpty()) {
                sorter.setRowFilter(null);
            } else {
                // filter on Nom (col 1) or Prénom (col 2)
                sorter.setRowFilter(RowFilter.regexFilter("(?i)" + text, 1, 2));
            }
        });
        btnClear.addActionListener(e -> {
            tfSearch.setText("");
            sorter.setRowFilter(null);
        });


        btnRef.addActionListener(e -> refresh());
        btnAdd.addActionListener(e -> addClient());
        btnEdit.addActionListener(e -> editClient());
        btnDel.addActionListener(e -> deleteClient());

        // initial load
        refresh();
    }

    private int idAt(int viewRow) {
        int modelRow = table.convertRowIndexToModel(viewRow);
        return (int) model.getValueAt(modelRow, 0);
    }

    private void refresh() {
        model.setRowCount(0);
        List<Client> list = dao.findAll();
        for (Client c : list) {
            model.addRow(new Object[]{
                    c.getIdClient(),
                    c.getNom(),
                    c.getPrenom(),
                    c.getAdresse(),
                    c.getTelephone(),
                    c.getEmail()
            });
        }
    }

    private void addClient() {
        JTextField nom       = new JTextField(),
                prenom    = new JTextField(),
                adresse   = new JTextField(),
                telephone = new JTextField(),
                email     = new JTextField();
        Object[] fields = {
                "Nom", nom,
                "Prénom", prenom,
                "Adresse", adresse,
                "Téléphone", telephone,
                "Email", email
        };
        if (JOptionPane.showConfirmDialog(this, fields, "Nouveau Client",
                JOptionPane.OK_CANCEL_OPTION) == JOptionPane.OK_OPTION) {
            try {
                Client c = new Client(
                        0,
                        nom.getText().trim(),
                        prenom.getText().trim(),
                        adresse.getText().trim(),
                        telephone.getText().trim(),
                        email.getText().trim()
                );
                dao.insert(c);
                refresh();
            } catch (Exception ex) {
                JOptionPane.showMessageDialog(this,
                        "Valeurs invalides !", "Erreur",
                        JOptionPane.ERROR_MESSAGE
                );
            }
        }
    }

    private void editClient() {
        int viewRow = table.getSelectedRow();
        if (viewRow < 0) return;
        int id = idAt(viewRow);
        Client c = dao.findById(id);

        JTextField nom       = new JTextField(c.getNom()),
                prenom    = new JTextField(c.getPrenom()),
                adresse   = new JTextField(c.getAdresse()),
                telephone = new JTextField(c.getTelephone()),
                email     = new JTextField(c.getEmail());
        Object[] fields = {
                "Nom", nom,
                "Prénom", prenom,
                "Adresse", adresse,
                "Téléphone", telephone,
                "Email", email
        };
        if (JOptionPane.showConfirmDialog(this, fields, "Modifier Client",
                JOptionPane.OK_CANCEL_OPTION) == JOptionPane.OK_OPTION) {
            c.setNom(nom.getText().trim());
            c.setPrenom(prenom.getText().trim());
            c.setAdresse(adresse.getText().trim());
            c.setTelephone(telephone.getText().trim());
            c.setEmail(email.getText().trim());
            dao.update(c);
            refresh();
        }
    }

    private void deleteClient() {
        int viewRow = table.getSelectedRow();
        if (viewRow < 0) return;
        int id = idAt(viewRow);
        if (JOptionPane.showConfirmDialog(this,
                "Êtes-vous sûr de vouloir supprimer ce client?",
                "Confirmation", JOptionPane.YES_NO_OPTION) == JOptionPane.YES_OPTION) {
            dao.delete(id);
            refresh();
        }
    }
}
