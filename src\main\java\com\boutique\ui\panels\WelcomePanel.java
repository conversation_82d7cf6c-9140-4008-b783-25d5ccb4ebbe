// src/main/java/com/boutique/ui/panels/WelcomePanel.java
package com.boutique.ui.panels;

import com.boutique.dao.impl.*;
import com.boutique.model.Commande;
import com.boutique.model.Client;
import com.boutique.model.Utilisateur;

import javax.swing.*;
import javax.swing.border.*;
import java.awt.*;
import java.util.List;

public class WelcomePanel extends JPanel {
    private static final Color   BG              = new Color(245, 245, 250);
    private static final Color   CARD_BG         = Color.WHITE;
    private static final Color   ACCENT_BLUE     = new Color(0, 150, 255);
    private static final Color   ACCENT_GREEN    = new Color(0, 200, 100);
    private static final Color   ACCENT_PURPLE   = new Color(200, 50, 255);
    private static final Color   ACCENT_ORANGE   = new Color(255, 140, 0);
    private static final Font    FONT_TITLE      = new Font("Segoe UI", Font.BOLD, 16);
    private static final Font    FONT_VALUE      = new Font("Segoe UI", Font.BOLD, 28);
    private static final Font    FONT_SECTION    = new Font("Segoe UI", Font.BOLD, 14);
    private static final Font    FONT_REC        = new Font("Segoe UI", Font.PLAIN, 14);

    public WelcomePanel(Utilisateur user) {
        setLayout(new BorderLayout());
        setBackground(BG);

        // === Statistique ===
        JPanel stats = new JPanel(new GridLayout(1, 5, 20, 20));
        stats.setBorder(new EmptyBorder(20, 20, 20, 20));
        stats.setBackground(BG);

        int prodCount   = new ProduitDAOImpl().findAll().size();
        int clientCount = new ClientDAOImpl().findAll().size();
        int fournCount  = new FournisseurDAOImpl().findAll().size();
        int userCount   = new UtilisateurDAOImpl().findAll().size();
        int cmdCount    = new CommandeDAOImpl().findAll().size();

        stats.add(makeCard("Produits", prodCount, ACCENT_BLUE));
        stats.add(makeCard("Clients", clientCount, ACCENT_GREEN));
        stats.add(makeCard("Fournisseurs", fournCount, ACCENT_PURPLE));
        stats.add(makeCard("Utilisateurs", userCount, ACCENT_ORANGE));
        stats.add(makeCard("Commandes", cmdCount, ACCENT_BLUE.darker()));

        add(stats, BorderLayout.NORTH);

        // === Activité récente ===
        List<Commande> cmds = new CommandeDAOImpl().findAll().stream()
                .sorted((a, b) -> b.getDateCommande().compareTo(a.getDateCommande()))
                .limit(5).toList();
        List<Client> clis = new ClientDAOImpl().findAll().stream().limit(5).toList();

        JPanel recContainer = new JPanel(new BorderLayout());
        recContainer.setBorder(new EmptyBorder(0, 20, 20, 20));
        recContainer.setBackground(BG);

        JLabel lblRecTitle = new JLabel("Activité Récente");
        lblRecTitle.setFont(FONT_TITLE);
        lblRecTitle.setForeground(ACCENT_BLUE);
        lblRecTitle.setBorder(new EmptyBorder(0, 0, 10, 0));
        recContainer.add(lblRecTitle, BorderLayout.NORTH);

        JPanel list = new JPanel();
        list.setLayout(new BoxLayout(list, BoxLayout.Y_AXIS));
        list.setBackground(CARD_BG);
        list.setBorder(new EmptyBorder(10, 10, 10, 10));

        list.add(makeSectionTitle("Commandes récentes", ACCENT_BLUE));
        for (Commande c : cmds) {
            list.add(makeRec("Commande #" + c.getIdCommande() + " par " + c.getClient().getNom()));
        }

        list.add(Box.createRigidArea(new Dimension(0, 15)));

        list.add(makeSectionTitle("Nouveaux clients", ACCENT_GREEN));
        for (Client c : clis) {
            list.add(makeRec(c.getNom() + " " + c.getPrenom()));
        }

        JScrollPane sp = new JScrollPane(list);
        sp.setBorder(new CompoundBorder(BorderFactory.createLineBorder(Color.LIGHT_GRAY), new EmptyBorder(5, 5, 5, 5)));
        recContainer.add(sp, BorderLayout.CENTER);

        add(recContainer, BorderLayout.CENTER);
    }

    private JPanel makeCard(String title, int value, Color accent) {
        JPanel card = new JPanel();
        card.setLayout(new BoxLayout(card, BoxLayout.Y_AXIS));
        card.setBackground(CARD_BG);
        card.setBorder(BorderFactory.createCompoundBorder(
                new CompoundBorder(
                        new LineBorder(accent, 2, true),
                        new MatteBorder(0, 0, 4, 0, new Color(230, 230, 230))
                ),
                new EmptyBorder(15, 20, 15, 20)
        ));

        JLabel t = new JLabel(title, SwingConstants.CENTER);
        t.setFont(FONT_SECTION);
        t.setAlignmentX(Component.CENTER_ALIGNMENT);
        t.setForeground(accent);

        JLabel v = new JLabel(String.valueOf(value), SwingConstants.CENTER);
        v.setFont(FONT_VALUE);
        v.setAlignmentX(Component.CENTER_ALIGNMENT);
        v.setForeground(new Color(60, 60, 60));

        card.add(t);
        card.add(Box.createRigidArea(new Dimension(0, 10)));
        card.add(v);

        return card;
    }

    private JLabel makeSectionTitle(String text, Color color) {
        JLabel lbl = new JLabel(text);
        lbl.setFont(FONT_SECTION);
        lbl.setForeground(color);
        lbl.setBorder(new EmptyBorder(5, 0, 5, 0));
        return lbl;
    }

    private JPanel makeRec(String text) {
        JLabel lbl = new JLabel("• " + text);
        lbl.setFont(FONT_REC);
        lbl.setBorder(new EmptyBorder(5, 10, 5, 10));

        JPanel p = new JPanel(new BorderLayout());
        p.setBackground(CARD_BG);
        p.add(lbl, BorderLayout.WEST);
        p.setBorder(BorderFactory.createMatteBorder(0, 0, 1, 0, Color.LIGHT_GRAY));
        return p;
    }
}
