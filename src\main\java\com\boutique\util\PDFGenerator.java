package com.boutique.util;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.font.PDType1Font;
import com.boutique.model.Commande;
import com.boutique.model.Cmd_prd_pivot;

import java.io.File;
import java.time.format.DateTimeFormatter;
import java.util.List;

public class PDFGenerator {

    public static void generateFacture(Commande cmd, String path) throws Exception {
        try (PDDocument doc = new PDDocument()) {
            PDPage page = new PDPage();
            doc.addPage(page);

            try (PDPageContentStream cs = new PDPageContentStream(doc, page)) {
                // Title
                cs.setFont(PDType1Font.HELVETICA_BOLD, 18);
                cs.beginText();
                cs.newLineAtOffset(50, 750);
                cs.showText("FACTURE");
                cs.endText();

                // Header info
                cs.setFont(PDType1Font.HELVETICA, 12);
                DateTimeFormatter df = DateTimeFormatter.ofPattern("dd/MM/yyyy");
                int y = 720;

                cs.beginText();
                cs.newLineAtOffset(50, y);
                cs.showText("N° facture : " + cmd.getIdCommande());
                cs.endText();

                y -= 15;
                cs.beginText();
                cs.newLineAtOffset(50, y);
                cs.showText("Date de facture : " + cmd.getDateCommande().format(df));
                cs.endText();

                y -= 15;
                cs.beginText();
                cs.newLineAtOffset(50, y);
                cs.showText("Client : "
                        + cmd.getClient().getNom() + " " + cmd.getClient().getPrenom());
                cs.endText();

                y -= 15;
                String user = cmd.getCreatedBy() != null
                        ? cmd.getCreatedBy()
                        : System.getProperty("user.name");
                cs.beginText();
                cs.newLineAtOffset(50, y);
                cs.showText("Utilisateur : " + user);
                cs.endText();

                // Table header
                y -= 30;
                cs.setFont(PDType1Font.HELVETICA_BOLD, 12);
                cs.beginText();
                cs.newLineAtOffset(50, y);
                cs.showText("Produit");
                cs.newLineAtOffset(200, 0);
                cs.showText("Qté");
                cs.newLineAtOffset(50, 0);
                cs.showText("PU (MAD)");
                cs.newLineAtOffset(60, 0);
                cs.showText("Sous-total");
                cs.endText();

                // Table rows
                cs.setFont(PDType1Font.HELVETICA, 12);
                List<Cmd_prd_pivot> lines = cmd.getLignesCommande();
                for (Cmd_prd_pivot line : lines) {
                    y -= 15;
                    cs.beginText();
                    cs.newLineAtOffset(50, y);
                    cs.showText(line.getProduit().getDescription());
                    cs.newLineAtOffset(200, 0);
                    cs.showText(String.valueOf(line.getQuantite()));
                    cs.newLineAtOffset(50, 0);
                    cs.showText(String.format("%.2f", line.getProduit().getPrix()));
                    cs.newLineAtOffset(60, 0);
                    cs.showText(String.format("%.2f",
                            line.getQuantite() * line.getProduit().getPrix()));
                    cs.endText();
                }

                // Total
                y -= 25;
                cs.setFont(PDType1Font.HELVETICA_BOLD, 14);
                cs.beginText();
                cs.newLineAtOffset(50, y);
                cs.showText("Montant total : "
                        + String.format("%.2f MAD", cmd.getTotal()));
                cs.endText();
            }

            doc.save(new File(path));
        }
    }
}
