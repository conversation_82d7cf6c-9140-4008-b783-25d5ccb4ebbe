package com.boutique.ui.panels;

import com.boutique.dao.impl.UtilisateurDAOImpl;
import com.boutique.model.Utilisateur;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.table.*;
import javax.swing.RowFilter;
import java.awt.*;
import java.util.List;

public class UtilisateurPanel extends JPanel {
    private final UtilisateurDAOImpl dao = new UtilisateurDAOImpl();
    private final DefaultTableModel model = new DefaultTableModel(new String[]{"ID", "Nom d'utilisateur", "Rôle"}, 0
    );
    private final JTable table = new JTable(model);
    private final TableRowSorter<DefaultTableModel> sorter =
            new TableRowSorter<>(model);

    // Search components
    private final JTextField tfSearch = new JTextField(20);
    private final JButton btnSearch   = new JButton("Rechercher");
    private final JButton btnClear    = new JButton("Effacer");

    public UtilisateurPanel(Utilisateur user) {
        setBackground(Color.WHITE);
        setBorder(new EmptyBorder(15,15,15,15));
        setLayout(new BorderLayout(5,5));

        // --- rechercher ---
        JPanel searchBar = new JPanel(new FlowLayout(FlowLayout.LEFT));
        searchBar.add(new JLabel("Recherche utilisateur:"));
        searchBar.add(tfSearch);
        searchBar.add(btnSearch);
        searchBar.add(btnClear);
        add(searchBar, BorderLayout.NORTH);

        // --- Tableau---
        table.setFillsViewportHeight(true);
        table.setRowHeight(24);
        table.setShowGrid(false);
        table.setSelectionBackground(new Color(93,173,226));
        table.setSelectionForeground(Color.WHITE);
        table.setRowSorter(sorter);
        add(new JScrollPane(table), BorderLayout.CENTER);

        // --- Control buttons ---
        JPanel ctrl = new JPanel();
        JButton btnAdd = new JButton("Ajouter");
        JButton btnEdit = new JButton("Modifier");
        JButton btnDel = new JButton("Supprimer");
        JButton btnRef = new JButton("Actualiser");
        ctrl.add(btnAdd);
        ctrl.add(btnEdit);
        ctrl.add(btnDel);
        ctrl.add(btnRef);
        add(ctrl, BorderLayout.SOUTH);

        // --- action de recheche ---
        btnSearch.addActionListener(e -> {
            String text = tfSearch.getText().trim();
            if (text.isEmpty()) {
                sorter.setRowFilter(null);
            } else {
                // filter on "Nom d'utilisateur" column (index 1)
                sorter.setRowFilter(RowFilter.regexFilter("(?i)" + text, 1));
            }
        });
        btnClear.addActionListener(e -> {tfSearch.setText("");sorter.setRowFilter(null);
        });

        // --- methode CRUD et actualisation ---
        btnRef.addActionListener(e -> refresh());
        btnAdd.addActionListener(e -> addUtilisateur());
        btnEdit.addActionListener(e -> editUtilisateur());
        btnDel.addActionListener(e -> deleteUtilisateur());

        // --donnes initianles du tableau ---
        refresh();
    }

    private int idAt(int viewRow) {
        int modelRow = table.convertRowIndexToModel(viewRow);
        return (int) model.getValueAt(modelRow, 0);
    }

    private void refresh() {
        model.setRowCount(0);
        List<Utilisateur> list = dao.findAll();
        for (Utilisateur u : list) {
            model.addRow(new Object[]{u.getIdUtilisateur(), u.getNomUtilisateur(), u.getRole()
            });
        }
    }

    private void addUtilisateur() {
        JTextField nom = new JTextField();
        JPasswordField mdp = new JPasswordField();
        String[] roles = {"Admin", "Vendeur"};
        JComboBox<String> role = new JComboBox<>(roles);

        Object[] f = {"Nom d'utilisateur", nom, "Mot de passe", mdp, "Rôle", role};
        if (JOptionPane.showConfirmDialog(this, f, "Nouvel Utilisateur", JOptionPane.OK_CANCEL_OPTION) == JOptionPane.OK_OPTION) {
            try {
                Utilisateur u = new Utilisateur(
                        0, nom.getText().trim(), new String(mdp.getPassword()), (String) role.getSelectedItem()
                );
                dao.insert(u);
                refresh();
            } catch (Exception ex) {
                JOptionPane.showMessageDialog(this, "Valeurs invalides !", "Erreur", JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    private void editUtilisateur() {
        int viewRow = table.getSelectedRow();
        if (viewRow < 0) return;
        int id = idAt(viewRow);
        Utilisateur u = dao.findById(id);

        JTextField nom = new JTextField(u.getNomUtilisateur());
        JPasswordField mdp = new JPasswordField(u.getMotDePasse());
        String[] roles = {"Admin", "Vendeur"};
        JComboBox<String> role = new JComboBox<>(roles);
        role.setSelectedItem(u.getRole());

        Object[] f = {"Nom d'utilisateur", nom, "Mot de passe", mdp, "Rôle", role};
        if (JOptionPane.showConfirmDialog(this, f, "Modifier Utilisateur", JOptionPane.OK_CANCEL_OPTION) == JOptionPane.OK_OPTION) {
            u.setNomUtilisateur(nom.getText().trim());
            u.setMotDePasse(new String(mdp.getPassword()));
            u.setRole((String) role.getSelectedItem());
            dao.update(u);
            refresh();
        }
    }

    private void deleteUtilisateur() {
        int viewRow = table.getSelectedRow();
        if (viewRow < 0) return;
        int id = idAt(viewRow);
        if (JOptionPane.showConfirmDialog(this, "Êtes-vous sûr de vouloir supprimer cet utilisateur?", "Confirmation", JOptionPane.YES_NO_OPTION) == JOptionPane.YES_OPTION) {
            dao.delete(id);
            refresh();
        }
    }
}
