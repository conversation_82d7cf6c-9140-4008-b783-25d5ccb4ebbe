// src/main/java/com/boutique/model/Produit.java
package com.boutique.model;

public class Produit {
    private int    idProduit;
    private String nom, description, categorie, picture;
    private double prix;
    private int    quantiteStock, fournisseurId;

    public Produit() {}

    /** Full constructor with supplier link and picture path. */
    public Produit(int idProduit,
                   String nom,
                   String description,
                   double prix,
                   int quantiteStock,
                   String categorie,
                   int fournisseurId,
                   String picture) {
        this.idProduit     = idProduit;
        this.nom           = nom;
        this.description   = description;
        this.prix          = prix;
        this.quantiteStock = quantiteStock;
        this.categorie     = categorie;
        this.fournisseurId = fournisseurId;
        this.picture       = picture;
    }

    /** Legacy constructor (defaults fournisseurId and picture) */
    public Produit(int idProduit,
                   String nom,
                   String description,
                   double prix,
                   int quantiteStock,
                   String categorie) {
        this(idProduit, nom, description, prix, quantiteStock, categorie, 0, "");
    }

    // ─── Getters & Setters ─────────────────────────────────

    public int getIdProduit() { return idProduit; }
    public void setIdProduit(int idProduit) { this.idProduit = idProduit; }

    public String getNom() { return nom; }
    public void setNom(String nom) { this.nom = nom; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public double getPrix() { return prix; }
    public void setPrix(double prix) { this.prix = prix; }

    public int getQuantiteStock() { return quantiteStock; }
    public void setQuantiteStock(int quantiteStock) { this.quantiteStock = quantiteStock; }

    public String getCategorie() { return categorie; }
    public void setCategorie(String categorie) { this.categorie = categorie; }

    public int getFournisseurId() { return fournisseurId; }
    public void setFournisseurId(int fournisseurId) { this.fournisseurId = fournisseurId; }

    public String getPicture() { return picture; }
    public void setPicture(String picture) { this.picture = picture; }

    public int getReorderThreshold() {
        return (int) Math.ceil(quantiteStock * 0.2);
    }

    public double getPrixUnitaire() {
        return prix;
    }

    /**
     * Make Swing components render a friendly label instead of "Produit@1234".
     */
    @Override
    public String toString() {
        return nom + " (MAD" + String.format("%.2f", prix) + ")";
    }


}
