package com.boutique.ui.panels;

import com.boutique.dao.impl.FournisseurDAOImpl;
import com.boutique.dao.impl.ProduitDAOImpl;
import com.boutique.model.Fournisseur;
import com.boutique.model.Produit;
import com.boutique.model.Utilisateur;

import javax.swing.*;
import javax.swing.table.*;
import javax.swing.RowFilter;
import javax.swing.event.DocumentEvent;
import javax.swing.event.DocumentListener;
import javax.swing.filechooser.FileNameExtensionFilter;
import java.awt.*;

import java.util.List;
import java.util.regex.Pattern;

public class ProduitPanel extends JPanel {
    private final ProduitDAOImpl     dao   = new ProduitDAOImpl();
    private final FournisseurDAOImpl fDao  = new FournisseurDAOImpl();

    private final DefaultTableModel               model;
    private final JTable                          table;
    private final TableRowSorter<DefaultTableModel> sorter;
    private final JTextField                      txtSearch;

    public ProduitPanel(Utilisateur user) {
        super(new BorderLayout(5,5));

        // --- Table produits ---
        model  = new DefaultTableModel(
                new String[]{"ID","Nom","Description","Prix (MAD)","Stock","Catégorie","Fournisseur","Image"}, 0
        );
        table  = new JTable(model);
        sorter = new TableRowSorter<>(model);
        table.setRowSorter(sorter);
        table.setRowHeight(24);
        table.setFillsViewportHeight(true);

        // --- Recherche (nord) ---
        txtSearch = new JTextField(20);
        JButton btnSearch  = new JButton("OK");
        JButton btnRefresh = new JButton("Actualiser");
        btnSearch.addActionListener(e -> applyFilter());
        btnRefresh.addActionListener(e -> {txtSearch.setText("");sorter.setRowFilter(null);loadProduits();
        });
        txtSearch.getDocument().addDocumentListener(new DocumentListener() {
            public void insertUpdate(DocumentEvent e){ applyFilter(); }
            public void removeUpdate(DocumentEvent e){ applyFilter(); }
            public void changedUpdate(DocumentEvent e){}
        });

        JPanel north = new JPanel(new FlowLayout(FlowLayout.LEFT,5,5));
        north.add(new JLabel("Chercher :"));
        north.add(txtSearch);
        north.add(btnSearch);
        north.add(btnRefresh);

        // --- CRUD (sud) ---
        JButton btnAdd  = new JButton("Ajouter");
        JButton btnEdit = new JButton("Modifier");
        JButton btnDel  = new JButton("Supprimer");

        btnAdd .addActionListener(e -> showForm(null));
        btnEdit.addActionListener(e -> {
            int r = table.getSelectedRow();
            if (r<0) return;
            int id = (int)model.getValueAt(table.convertRowIndexToModel(r),0);
            showForm(dao.findById(id));
        });
        btnDel .addActionListener(e -> {
            int r = table.getSelectedRow();
            if (r<0) return;
            int id = (int)model.getValueAt(table.convertRowIndexToModel(r),0);
            if (JOptionPane.showConfirmDialog(this,"Supprimer ce produit ?","Confirmer", JOptionPane.YES_NO_OPTION)==JOptionPane.YES_OPTION) {
                dao.delete(id);
                loadProduits();
            }
        });

        // **Vendeur**: lecture seule sur les produits
        if (user.isVendeur()) {
            btnAdd .setEnabled(false);
            btnEdit.setEnabled(false);
            btnDel .setEnabled(false);
        }

        JPanel south = new JPanel(new FlowLayout(FlowLayout.LEFT,5,5));
        south.add(btnAdd);
        south.add(btnEdit);
        south.add(btnDel);

        // --- Montage final ---
        add(north, BorderLayout.NORTH);
        add(new JScrollPane(table), BorderLayout.CENTER);
        add(south, BorderLayout.SOUTH);

        loadProduits();
    }

    private void applyFilter() {
        String q = txtSearch.getText().trim();
        sorter.setRowFilter(q.isEmpty() ? null : RowFilter.regexFilter("(?i)" + Pattern.quote(q))
        );
    }

    private void loadProduits() {
        model.setRowCount(0);
        List<Produit> list = dao.findAll();
        for (Produit p : list) {
            Fournisseur f = fDao.findById(p.getFournisseurId());
            model.addRow(new Object[]{p.getIdProduit(), p.getNom(), p.getDescription(), String.format("%.2f", p.getPrix()), p.getQuantiteStock(), p.getCategorie(), f != null ? f.getNom() : "", p.getPicture()
            });
        }
    }

    private void showForm(Produit existing) {
        JDialog dlg = new JDialog(SwingUtilities.getWindowAncestor(this), existing==null ? "Ajouter produit" : "Modifier produit", Dialog.ModalityType.APPLICATION_MODAL
        );
        dlg.setLayout(new GridBagLayout());
        GridBagConstraints c = new GridBagConstraints();
        c.insets = new Insets(4,4,4,4);
        c.anchor = GridBagConstraints.WEST;

        // Champs
        JTextField tfNom   = new JTextField(20);
        JTextField tfDesc  = new JTextField(20);
        JTextField tfPrix  = new JTextField(10);
        JTextField tfStock = new JTextField(5);
        JTextField tfCat   = new JTextField(10);
        JComboBox<Fournisseur> cbFourn = new JComboBox<>();
        fDao.findAll().forEach(cbFourn::addItem);
        JTextField tfImg  = new JTextField(20);
        JButton btnBrowse = new JButton("Parcourir...");

        if (existing!=null) {
            tfNom  .setText(existing.getNom());
            tfDesc .setText(existing.getDescription());
            tfPrix .setText(String.valueOf(existing.getPrix()));
            tfStock.setText(String.valueOf(existing.getQuantiteStock()));
            tfCat  .setText(existing.getCategorie());
            fDao.findAll().stream().filter(f -> f.getIdFournisseur()==existing.getFournisseurId()).findFirst().ifPresent(cbFourn::setSelectedItem);
            tfImg.setText(existing.getPicture());
        }

        int y=0;
        c.gridx=0; c.gridy=y;           dlg.add(new JLabel("Nom :"), c);
        c.gridx=1;                      dlg.add(tfNom, c);
        c.gridy=++y; c.gridx=0;         dlg.add(new JLabel("Description :"), c);
        c.gridx=1;                      dlg.add(tfDesc, c);
        c.gridy=++y; c.gridx=0;         dlg.add(new JLabel("Prix (MAD) :"), c);
        c.gridx=1;                      dlg.add(tfPrix, c);
        c.gridy=++y; c.gridx=0;         dlg.add(new JLabel("Quantité :"), c);
        c.gridx=1;                      dlg.add(tfStock, c);
        c.gridy=++y; c.gridx=0;         dlg.add(new JLabel("Catégorie :"), c);
        c.gridx=1;                      dlg.add(tfCat, c);
        c.gridy=++y; c.gridx=0;         dlg.add(new JLabel("Fournisseur :"), c);
        c.gridx=1;                      dlg.add(cbFourn, c);
        c.gridy=++y; c.gridx=0;         dlg.add(new JLabel("Image (path) :"), c);
        c.gridx=1;                      dlg.add(tfImg, c);
        c.gridx=2;                      dlg.add(btnBrowse, c);

        btnBrowse.addActionListener(e-> {
            JFileChooser fc = new JFileChooser();
            fc.setFileFilter(new FileNameExtensionFilter("Images","jpg","png","gif","jpeg"));
            if (fc.showOpenDialog(dlg)==JFileChooser.APPROVE_OPTION) {
                tfImg.setText(fc.getSelectedFile().getAbsolutePath());
            }
        });

        // Save / Cancel
        JPanel pnl = new JPanel();
        JButton btnSave   = new JButton("Enregistrer");
        JButton btnCancel = new JButton("Annuler");
        pnl.add(btnSave);
        pnl.add(btnCancel);
        c.gridy=++y; c.gridx=0; c.gridwidth=3; c.anchor=GridBagConstraints.CENTER;
        dlg.add(pnl, c);

        btnSave.addActionListener(e-> {
            try {
                Produit p = existing==null ? new Produit() : existing;
                p.setNom(tfNom.getText().trim());
                p.setDescription(tfDesc.getText().trim());
                p.setPrix(Double.parseDouble(tfPrix.getText().trim()));
                p.setQuantiteStock(Integer.parseInt(tfStock.getText().trim()));
                p.setCategorie(tfCat.getText().trim());
                Fournisseur f = (Fournisseur)cbFourn.getSelectedItem();
                p.setFournisseurId(f!=null?f.getIdFournisseur():0);
                p.setPicture(tfImg.getText().trim());

                if (existing==null) dao.insert(p);
                else dao.update(p);

                dlg.dispose();
                loadProduits();
            } catch (Exception ex) {
                JOptionPane.showMessageDialog(
                        dlg, "Erreur : "+ex.getMessage(),
                        "Erreur", JOptionPane.ERROR_MESSAGE
                );
            }
        });
        btnCancel.addActionListener(e-> dlg.dispose());

        dlg.pack();
        dlg.setLocationRelativeTo(this);
        dlg.setVisible(true);
    }
}
