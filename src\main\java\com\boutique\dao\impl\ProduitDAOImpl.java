// src/main/java/com/boutique/dao/impl/ProduitDAOImpl.java
package com.boutique.dao.impl;

import com.boutique.dao.GenericDAO;
import com.boutique.model.Produit;
import com.boutique.util.DBConnection;

import java.sql.*;
import java.util.*;

public class ProduitDAOImpl implements GenericDAO<Produit> {
    @Override
    public Produit findById(int id) {
        String sql = "SELECT * FROM Produit WHERE idProduit = ?";
        try (var c = DBConnection.getConnection();
             var ps = c.prepareStatement(sql)) {
            ps.setInt(1,id);
            try (var rs = ps.executeQuery()) {
                if (!rs.next()) return null;
                return new Produit(
                        rs.getInt("idProduit"),
                        rs.getString("nom"),
                        rs.getString("description"),
                        rs.getDouble("prix"),
                        rs.getInt("quantiteStock"),
                        rs.getString("categorie"),
                        rs.getInt("fournisseurId"),
                        rs.getString("picture")
                );
            }
        } catch(SQLException e){ throw new RuntimeException(e); }
    }

    @Override
    public List<Produit> findAll() {
        List<Produit> out = new ArrayList<>();
        String sql = "SELECT * FROM Produit";
        try (var c = DBConnection.getConnection();
             var st = c.createStatement();
             var rs = st.executeQuery(sql)) {
            while (rs.next()) {
                out.add(new Produit(
                        rs.getInt("idProduit"),
                        rs.getString("nom"),
                        rs.getString("description"),
                        rs.getDouble("prix"),
                        rs.getInt("quantiteStock"),
                        rs.getString("categorie"),
                        rs.getInt("fournisseurId"),
                        rs.getString("picture")
                ));
            }
        } catch(SQLException e){ throw new RuntimeException(e); }
        return out;
    }

    @Override
    public void insert(Produit p) {
        String sql = "INSERT INTO Produit(nom,description,prix,quantiteStock,categorie,fournisseurId,picture) VALUES(?,?,?,?,?,?,?)";
        try (var c = DBConnection.getConnection();
             var ps = c.prepareStatement(sql,Statement.RETURN_GENERATED_KEYS)) {
            ps.setString(1,p.getNom());
            ps.setString(2,p.getDescription());
            ps.setDouble(3,p.getPrix());
            ps.setInt(4,p.getQuantiteStock());
            ps.setString(5,p.getCategorie());
            ps.setInt(6,p.getFournisseurId());
            ps.setString(7,p.getPicture());
            ps.executeUpdate();
            try(var ks=ps.getGeneratedKeys()){ if(ks.next()) p.setIdProduit(ks.getInt(1)); }
        } catch(SQLException e){ throw new RuntimeException(e); }
    }

    @Override
    public void update(Produit p) {
        String sql = "UPDATE Produit SET nom=?,description=?,prix=?,quantiteStock=?,categorie=?,fournisseurId=?,picture=? WHERE idProduit=?";
        try (var c = DBConnection.getConnection();
             var ps = c.prepareStatement(sql)) {
            ps.setString(1,p.getNom());
            ps.setString(2,p.getDescription());
            ps.setDouble(3,p.getPrix());
            ps.setInt(4,p.getQuantiteStock());
            ps.setString(5,p.getCategorie());
            ps.setInt(6,p.getFournisseurId());
            ps.setString(7,p.getPicture());
            ps.setInt(8,p.getIdProduit());
            ps.executeUpdate();
        } catch(SQLException e){ throw new RuntimeException(e); }
    }

    @Override
    public void delete(int id) {
        String sql = "DELETE FROM Produit WHERE idProduit=?";
        try (var c = DBConnection.getConnection();
             var ps = c.prepareStatement(sql)) {
            ps.setInt(1,id);
            ps.executeUpdate();
        } catch(SQLException e){ throw new RuntimeException(e); }
    }
}
