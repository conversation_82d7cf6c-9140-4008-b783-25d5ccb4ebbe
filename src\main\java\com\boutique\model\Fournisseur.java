package com.boutique.model;

import java.util.ArrayList;
import java.util.List;

public class Fournisseur {
    private int idFournisseur;
    private String nom;
    private String contact;
    private List<Produit> produitsFournis;

    public Fournisseur(int idFournisseur, String nom, String contact) {
        this.idFournisseur = idFournisseur;
        this.nom = nom;
        this.contact = contact;
        this.produitsFournis = new ArrayList<>();
    }

    public int getIdFournisseur() {
        return idFournisseur;
    }

    public void setIdFournisseur(int idFournisseur) {
        this.idFournisseur = idFournisseur;
    }

    public String getNom() {
        return nom;
    }

    public void setNom(String nom) {
        this.nom = nom;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public List<Produit> getProduitsFournis() {
        return produitsFournis;
    }

    public void setProduitsFournis(List<Produit> produitsFournis) {
        this.produitsFournis = produitsFournis;
    }

    public void addProduit(Produit produit) {
        if (!produitsFournis.contains(produit)) {
            produitsFournis.add(produit);
        }
    }

    public void removeProduit(Produit produit) {
        produitsFournis.remove(produit);
    }

    @Override
    public String toString() {
        return nom;
    }
}
