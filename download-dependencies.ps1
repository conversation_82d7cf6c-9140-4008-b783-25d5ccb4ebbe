Write-Host "Downloading required dependencies..." -ForegroundColor Green

# Create lib directory if it doesn't exist
if (!(Test-Path "lib")) {
    New-Item -ItemType Directory -Path "lib"
    Write-Host "Created lib directory" -ForegroundColor Blue
}

# Download MySQL Connector/J
$mysqlUrl = "https://repo1.maven.org/maven2/mysql/mysql-connector-java/8.0.33/mysql-connector-java-8.0.33.jar"
$mysqlPath = "lib\mysql-connector-java-8.0.33.jar"

if (!(Test-Path $mysqlPath)) {
    Write-Host "Downloading MySQL Connector/J..." -ForegroundColor Yellow
    try {
        Invoke-WebRequest -Uri $mysqlUrl -OutFile $mysqlPath
        Write-Host "✓ MySQL Connector downloaded successfully" -ForegroundColor Green
    } catch {
        Write-Host "✗ Failed to download MySQL Connector: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Please download manually from: $mysqlUrl" -ForegroundColor Yellow
    }
} else {
    Write-Host "✓ MySQL Connector already exists" -ForegroundColor Green
}

# Download Hibernate Core (optional, for full functionality)
$hibernateUrl = "https://repo1.maven.org/maven2/org/hibernate/orm/hibernate-core/6.2.7.Final/hibernate-core-6.2.7.Final.jar"
$hibernatePath = "lib\hibernate-core-6.2.7.Final.jar"

if (!(Test-Path $hibernatePath)) {
    Write-Host "Downloading Hibernate Core..." -ForegroundColor Yellow
    try {
        Invoke-WebRequest -Uri $hibernateUrl -OutFile $hibernatePath
        Write-Host "✓ Hibernate Core downloaded successfully" -ForegroundColor Green
    } catch {
        Write-Host "✗ Failed to download Hibernate Core: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Please download manually from: $hibernateUrl" -ForegroundColor Yellow
    }
} else {
    Write-Host "✓ Hibernate Core already exists" -ForegroundColor Green
}

# Download PDFBox
$pdfboxUrl = "https://repo1.maven.org/maven2/org/apache/pdfbox/pdfbox/2.0.27/pdfbox-2.0.27.jar"
$pdfboxPath = "lib\pdfbox-2.0.27.jar"

if (!(Test-Path $pdfboxPath)) {
    Write-Host "Downloading PDFBox..." -ForegroundColor Yellow
    try {
        Invoke-WebRequest -Uri $pdfboxUrl -OutFile $pdfboxPath
        Write-Host "✓ PDFBox downloaded successfully" -ForegroundColor Green
    } catch {
        Write-Host "✗ Failed to download PDFBox: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Please download manually from: $pdfboxUrl" -ForegroundColor Yellow
    }
} else {
    Write-Host "✓ PDFBox already exists" -ForegroundColor Green
}

Write-Host ""
Write-Host "Dependencies download complete!" -ForegroundColor Green
Write-Host "You can now run the application using run-boutique.ps1" -ForegroundColor Blue

Read-Host "Press Enter to exit"
