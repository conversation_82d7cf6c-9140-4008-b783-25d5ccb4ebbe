package com.boutique.ui.panels;

import com.boutique.dao.impl.FournisseurDAOImpl;
import com.boutique.model.Fournisseur;
import com.boutique.model.Utilisateur;

import javax.swing.*;
import javax.swing.table.*;
import javax.swing.RowFilter;
import java.awt.*;
import java.util.List;

public class FournisseurPanel extends JPanel {
    private final FournisseurDAOImpl dao = new FournisseurDAOImpl();
    private final DefaultTableModel model = new DefaultTableModel(
            new String[]{"ID", "Nom", "Contact"}, 0
    );
    private final JTable table = new JTable(model);
    private final TableRowSorter<DefaultTableModel> sorter =
            new TableRowSorter<>(model);

    // Search components
    private final JTextField tfSearch = new JTextField(20);
    private final JButton btnSearch   = new JButton("Rechercher");
    private final JButton btnClear    = new JButton("Effacer");

    public FournisseurPanel(Utilisateur user) {
        setLayout(new BorderLayout(5,5));
        setBackground(Color.WHITE);

        // --- Search bar ---
        JPanel searchBar = new JPanel(new FlowLayout(FlowLayout.LEFT));
        searchBar.setBackground(Color.WHITE);
        searchBar.add(new JLabel("Recherche fournisseur:"));
        searchBar.add(tfSearch);
        searchBar.add(btnSearch);
        searchBar.add(btnClear);
        add(searchBar, BorderLayout.NORTH);

        // --- Table setup ---
        table.setFillsViewportHeight(true);
        table.setRowHeight(24);
        table.setShowGrid(false);
        table.setSelectionBackground(new Color(93, 173, 226));
        table.setSelectionForeground(Color.WHITE);
        table.setRowSorter(sorter);
        add(new JScrollPane(table), BorderLayout.CENTER);

        // --- Control buttons ---
        JPanel ctrl = new JPanel();
        ctrl.setBackground(Color.WHITE);
        JButton btnAdd = new JButton("Ajouter");
        JButton btnEdit = new JButton("Modifier");
        JButton btnDel = new JButton("Supprimer");
        JButton btnRef = new JButton("Actualiser");
        ctrl.add(btnAdd);
        ctrl.add(btnEdit);
        ctrl.add(btnDel);
        ctrl.add(btnRef);
        add(ctrl, BorderLayout.SOUTH);

        // --- Wire search actions ---
        btnSearch.addActionListener(e -> {
            String text = tfSearch.getText().trim();
            if (text.isEmpty()) {
                sorter.setRowFilter(null);
            } else {
                // filter on "Nom" column (index 1)
                sorter.setRowFilter(RowFilter.regexFilter("(?i)" + text, 1));
            }
        });
        btnClear.addActionListener(e -> {
            tfSearch.setText("");
            sorter.setRowFilter(null);
        });

        // --- Wire CRUD & refresh ---
        btnRef.addActionListener(e -> refresh());

        btnAdd.addActionListener(e -> showForm(null));

        btnEdit.addActionListener(e -> {
            int viewRow = table.getSelectedRow();
            if (viewRow < 0) return;
            int modelRow = table.convertRowIndexToModel(viewRow);
            int id = (int) model.getValueAt(modelRow, 0);
            Fournisseur f = dao.findById(id);
            showForm(f);
        });

        btnDel.addActionListener(e -> {
            int viewRow = table.getSelectedRow();
            if (viewRow < 0) return;
            int modelRow = table.convertRowIndexToModel(viewRow);
            int id = (int) model.getValueAt(modelRow, 0);
            if (JOptionPane.showConfirmDialog(this,
                    "Êtes-vous sûr de vouloir supprimer ce fournisseur?",
                    "Confirmation", JOptionPane.YES_NO_OPTION) == JOptionPane.YES_OPTION) {
                dao.delete(id);
                refresh();
            }
        });

        // initial load
        refresh();
    }

    private void showForm(Fournisseur existing) {
        JTextField tfNom     = new JTextField();
        JTextField tfContact = new JTextField();

        if (existing != null) {
            tfNom.setText(existing.getNom());
            tfContact.setText(existing.getContact());
        }

        Object[] fields = {
                "Nom :", tfNom,
                "Contact :", tfContact
        };

        String title = (existing == null) ? "Nouveau Fournisseur" : "Modifier Fournisseur";
        if (JOptionPane.showConfirmDialog(
                this, fields, title, JOptionPane.OK_CANCEL_OPTION
        ) == JOptionPane.OK_OPTION) {
            String nom = tfNom.getText().trim();
            String contact = tfContact.getText().trim();

            if (existing == null) {
                dao.insert(new Fournisseur(0, nom, contact));
            } else {
                existing.setNom(nom);
                existing.setContact(contact);
                dao.update(existing);
            }
            refresh();
        }
    }

    private void refresh() {
        model.setRowCount(0);
        List<Fournisseur> list = dao.findAll();
        for (Fournisseur f : list) {
            model.addRow(new Object[]{
                    f.getIdFournisseur(),
                    f.getNom(),
                    f.getContact()
            });
        }
    }
}
