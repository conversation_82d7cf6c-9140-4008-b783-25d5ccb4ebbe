
package com.boutique.ui;

import com.boutique.dao.impl.UtilisateurDAOImpl;
import com.boutique.model.Utilisateur;
import com.boutique.util.SessionManager;
import com.boutique.util.StockAlertService;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import java.awt.*;
import java.net.URL;

public class LoginFrame extends JFrame {
    static {

        try {
            for (UIManager.LookAndFeelInfo info :
                    UIManager.getInstalledLookAndFeels()) {
                if ("Nimbus".equals(info.getName())) {
                    UIManager.setLookAndFeel(info.getClassName());
                    break;
                }
            }
            UIManager.put("control",        new Color(250,250,250));
            UIManager.put("nimbusBase",     new Color(50,50,50));
            UIManager.put("nimbusBlueGrey", new Color(200,200,200));
            UIManager.put("nimbusFocus",    new Color(93,173,226));
            UIManager.put("textForeground", Color.DARK_GRAY);
            UIManager.put("defaultFont",    new Font("Segoe UI", Font.PLAIN, 14));
        } catch (Exception ignored) {}
    }

    private final JTextField      tfUser = new JTextField(15);
    private final JPasswordField  pfPass = new JPasswordField(15);
    private final UtilisateurDAOImpl dao = new UtilisateurDAOImpl();

    public LoginFrame() {
        super("Gestion de Boutique");
        setDefaultCloseOperation(EXIT_ON_CLOSE);
        setSize(1100, 800);
        setLocationRelativeTo(null);
        setLayout(new BorderLayout());

        // ─ panel de gauche pour image et titre ─────────────────────────────────────────
        JPanel left = new JPanel(new BorderLayout());
        left.setBackground(new Color(3, 5, 44, 131));
        left.setPreferredSize(new Dimension(450, 0));

        // 250×250 dimension f fct lt7t
        JLabel img = new JLabel(loadScaledIcon());
        img.setHorizontalAlignment(SwingConstants.CENTER);
        left.add(img, BorderLayout.CENTER);

        JLabel tagline = new JLabel("Gestion de votre boutique en un click", SwingConstants.CENTER
        );
        tagline.setForeground(Color.WHITE);
        tagline.setBorder(new EmptyBorder(15, 15, 25, 15));
        left.add(tagline, BorderLayout.SOUTH);

        add(left, BorderLayout.WEST);

        // ─ lformulaire ta3 login avec username password ───────────────────────────────────────────────
        JPanel right = new JPanel(new GridBagLayout());
        right.setBackground(Color.WHITE);
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(18, 18, 18, 18);


        gbc.anchor = GridBagConstraints.CENTER;

        // Titre
        gbc.gridx = 0; gbc.gridy = 0; gbc.gridwidth = 2;
        JLabel lblTitle = new JLabel("Connexion");
        lblTitle.setFont(new Font("Segoe UI", Font.BOLD, 24));
        right.add(lblTitle, gbc);

        // hdra
        gbc.gridy = 1;
        JLabel lblSub = new JLabel("Veuillez entrer vos identifiants");
        lblSub.setFont(new Font("Segoe UI", Font.PLAIN, 15));
        right.add(lblSub, gbc);

        // Username
        gbc.gridwidth = 1;
        gbc.gridy = 2; gbc.gridx = 0;
        right.add(new JLabel("Nom d'utilisateur"), gbc);
        gbc.gridx = 1;
        right.add(tfUser, gbc);

        // Password
        gbc.gridy = 3; gbc.gridx = 0;
        right.add(new JLabel("Mot de passe"), gbc);
        gbc.gridx = 1;
        right.add(pfPass, gbc);

        // buttona cnx
        gbc.gridy = 4; gbc.gridx = 0; gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        JButton btn = new JButton("Se connecter");
        btn.setBackground(new Color(34, 34, 34));
        btn.setForeground(Color.WHITE);
        btn.setFont(new Font("Segoe UI", Font.BOLD, 16));
        btn.setCursor(Cursor.getPredefinedCursor(Cursor.HAND_CURSOR));
        btn.addActionListener(e -> authenticate());
        right.add(btn, gbc);

        add(right, BorderLayout.CENTER);
    }

    private void authenticate() {
        String user = tfUser.getText().trim();
        String pass = new String(pfPass.getPassword()).trim();
        for (Utilisateur u : dao.findAll()) {
            if (u.getNomUtilisateur().equals(user) && u.getMotDePasse().equals(pass)) {
                SessionManager.setCurrentUser(u);
                SwingUtilities.invokeLater(() -> new MainFrame(u).setVisible(true));
                dispose();
                return;
            }
        }
        JOptionPane.showMessageDialog(this, "Identifiants invalides", "Erreur", JOptionPane.ERROR_MESSAGE
        );
    }


    private Icon loadScaledIcon() {
        URL url = getClass().getResource("/6681204.png");
        Image img = new ImageIcon(url).getImage();
        Image scaled = img.getScaledInstance(250, 250, Image.SCALE_SMOOTH);
        return new ImageIcon(scaled);
    }

    public static void main(String[] args) {
        StockAlertService.initSystemTray();
        SwingUtilities.invokeLater(() -> new LoginFrame().setVisible(true));

    }
}
