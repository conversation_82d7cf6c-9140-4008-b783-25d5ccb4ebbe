// src/main/java/com/boutique/ui/panels/CommandePanel.java
package com.boutique.ui.panels;

import com.boutique.dao.impl.CommandeDAOImpl;
import com.boutique.dao.impl.ClientDAOImpl;
import com.boutique.dao.impl.ProduitDAOImpl;
import com.boutique.model.Cmd_prd_pivot;
import com.boutique.model.Client;
import com.boutique.model.Commande;
import com.boutique.model.Produit;
import com.boutique.model.Utilisateur;
import com.boutique.util.PDFGenerator;
import com.boutique.util.SessionManager;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableRowSorter;
import java.awt.*;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

public class CommandePanel extends JPanel {
    private final CommandeDAOImpl commandeDao = new CommandeDAOImpl();
    private final ClientDAOImpl    clientDao   = new ClientDAOImpl();
    private final ProduitDAOImpl   produitDao  = new ProduitDAOImpl();
    private static final DateTimeFormatter DTF = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private final DefaultTableModel               model;
    private final JTable                          table;
    private final TableRowSorter<DefaultTableModel> sorter;

    public CommandePanel(Utilisateur user) {
        super(new BorderLayout(10, 10));

        // 1) Table & modèle
        model  = new DefaultTableModel(new String[]{"ID", "Date", "Client", "Créé par", "Total (MAD)", "Statut"}, 0);
        table  = new JTable(model);
        sorter = new TableRowSorter<>(model);
        table.setRowSorter(sorter);
        table.setFillsViewportHeight(true);
        table.setRowHeight(24);

        // 2) Barre de recherche (NORD)
        JPanel north = new JPanel(new FlowLayout(FlowLayout.LEFT, 5, 5));
        JTextField searchField = new JTextField(15);
        JButton btnSearch  = new JButton("OK");
        JButton btnClear   = new JButton("Effacer");
        JButton btnRefresh = new JButton("Actualiser");

        btnSearch.addActionListener(e -> {String t = searchField.getText().trim();
            sorter.setRowFilter(t.isEmpty() ? null : RowFilter.regexFilter("(?i)" + t, 2));
        });
        btnClear.addActionListener(e -> {searchField.setText("");
            sorter.setRowFilter(null);
        });
        btnRefresh.addActionListener(e -> loadCommandes());

        north.add(new JLabel("Recherche client :"));
        north.add(searchField);
        north.add(btnSearch);
        north.add(btnClear);
        north.add(btnRefresh);
        add(north, BorderLayout.NORTH);

        // 3) Boutons d'action (SUD)
        JPanel south = new JPanel(new FlowLayout(FlowLayout.LEFT, 5, 5));
        JButton btnAdd     = new JButton("Ajouter");
        JButton btnEdit    = new JButton("Modifier");
        JButton btnDelete  = new JButton("Supprimer");
        JButton btnDetails = new JButton("Détails");
        JButton btnInvoice = new JButton("Facture");

        btnAdd .addActionListener(e -> showForm(null));
        btnEdit.addActionListener(e -> {
            Commande c = getSelected();
            if (c != null) showForm(c);
        });
        btnDelete .addActionListener(e -> {
            Commande c = getSelected();
            if (c != null && confirm("Supprimer cette commande ?")) {
                commandeDao.delete(c.getIdCommande());
                loadCommandes();
            }
        });
        btnDetails.addActionListener(e -> {
            Commande c = getSelected();
            if (c != null) showDetails(c);
        });
        btnInvoice.addActionListener(e -> {
            Commande c = getSelected();
            if (c != null) invoice(c);
        });





        for (JButton b : List.of(btnAdd, btnEdit, btnDelete, btnDetails, btnInvoice)) {
            south.add(b);
        }
        add(south, BorderLayout.SOUTH);

        // 4) Centre : tableau scrollé
        add(new JScrollPane(table), BorderLayout.CENTER);

        // Chargement initial
        loadCommandes();
    }

    private void loadCommandes() {
        model.setRowCount(0);
        for (Commande c : commandeDao.findAll()) {
            model.addRow(new Object[]{
                    c.getIdCommande(),
                    c.getDateCommande().format(DTF),
                    c.getClient().getNom() + " " + c.getClient().getPrenom(),
                    c.getCreatedBy(),
                    String.format("%.2f", c.getTotal()),
                    c.getStatut()
            });
        }
    }

    private Commande getSelected() {
        int row = table.getSelectedRow();
        if (row < 0) return null;
        int modelRow = table.convertRowIndexToModel(row);
        int id = Integer.parseInt(model.getValueAt(modelRow, 0).toString());
        return commandeDao.findById(id);
    }

    private boolean confirm(String msg) {
        return JOptionPane.showConfirmDialog(
                this, msg, "Confirmer", JOptionPane.YES_NO_OPTION
        ) == JOptionPane.YES_OPTION;
    }

    private void showForm(Commande existing) {
        JDialog dlg = new JDialog(
                SwingUtilities.getWindowAncestor(this),
                existing == null ? "Nouvelle commande" : "Modifier commande",
                Dialog.ModalityType.APPLICATION_MODAL
        );
        dlg.setLayout(new BorderLayout(10, 10));

        // --- Haut : date / client / statut ---
        JLabel lblDate = new JLabel("Date : " +
                (existing != null ? existing.getDateCommande().format(DTF) : LocalDate.now().format(DTF)
                )
        );
        JComboBox<Client> cbClient = new JComboBox<>(
                clientDao.findAll().toArray(new Client[0])
        );
        if (existing != null) cbClient.setSelectedItem(existing.getClient());

        JComboBox<String> cbStatut = new JComboBox<>(
                new String[]{"En attente", "Expédiée", "Annulée"}
        );
        cbStatut.setSelectedItem(existing != null ? existing.getStatut() : "En attente");

        JPanel top = new JPanel(new GridLayout(3, 2, 5, 5));
        top.add(new JLabel("Date :"));    top.add(lblDate);
        top.add(new JLabel("Client :"));  top.add(cbClient);
        top.add(new JLabel("Statut :"));  top.add(cbStatut);
        dlg.add(top, BorderLayout.NORTH);

        // --- Milieu : lignes de commande ---
        DefaultTableModel lm = new DefaultTableModel(
                new String[]{"Produit", "Qté"}, 0
        );
        JTable lt = new JTable(lm);
        if (existing != null) {
            for (Cmd_prd_pivot l : existing.getLignesCommande()) {
                lm.addRow(new Object[]{l.getProduit(), l.getQuantite()});
            }
        }

        JButton btnAddProd = new JButton("Ajouter produit");
        btnAddProd.addActionListener(e -> {
            Produit[] prods = produitDao.findAll().toArray(new Produit[0]);
            Produit sel = (Produit) JOptionPane.showInputDialog(
                    dlg, "Produit", "Ajouter produit",
                    JOptionPane.PLAIN_MESSAGE, null, prods,
                    prods.length > 0 ? prods[0] : null
            );
            if (sel != null) {
                String s = JOptionPane.showInputDialog(dlg, "Quantité", "1");
                try {
                    lm.addRow(new Object[]{sel, Integer.parseInt(s)});
                } catch (Exception ignore) {}
            }
        });

        JButton btnRemProd = new JButton("Suppr. produit");
        btnRemProd.addActionListener(e -> {
            int sel = lt.getSelectedRow();
            if (sel >= 0) lm.removeRow(sel);
        });

        JPanel mid = new JPanel(new BorderLayout(5, 5));
        JPanel prodBtns = new JPanel(new FlowLayout(FlowLayout.LEFT, 5, 5));
        prodBtns.add(btnAddProd);
        prodBtns.add(btnRemProd);
        mid.add(prodBtns, BorderLayout.NORTH);
        mid.add(new JScrollPane(lt), BorderLayout.CENTER);
        dlg.add(mid, BorderLayout.CENTER);

        // --- Bas : enregistrer / annuler ---
        JButton btnSave   = new JButton("Enregistrer");
        JButton btnCancel = new JButton("Annuler");

        btnSave.addActionListener(e -> {
            try {
                boolean isNew = (existing == null);
                Commande cmd = isNew ? new Commande() : existing;

                if (isNew) {
                    cmd.setDateCommande(LocalDate.now());
                    cmd.setCreatedBy(SessionManager.getCurrentUser().getNomUtilisateur());
                }

                cmd.setClient((Client) cbClient.getSelectedItem());
                cmd.setStatut((String) cbStatut.getSelectedItem());

                List<Cmd_prd_pivot> lignes = new ArrayList<>();
                for (int i = 0; i < lm.getRowCount(); i++) {
                    Produit p = (Produit) lm.getValueAt(i, 0);
                    int q    = Integer.parseInt(lm.getValueAt(i, 1).toString());
                    lignes.add(new Cmd_prd_pivot(p, q));
                }
                cmd.setLignesCommande(lignes);

                if (isNew) commandeDao.insert(cmd);
                else       commandeDao.update(cmd);

                dlg.dispose();
                loadCommandes();
            } catch (Exception ex) {
                JOptionPane.showMessageDialog(
                        dlg, "Erreur : " + ex.getMessage(),
                        "Erreur", JOptionPane.ERROR_MESSAGE
                );
            }
        });

        btnCancel.addActionListener(e -> dlg.dispose());

        JPanel bottom = new JPanel(new FlowLayout(FlowLayout.RIGHT, 5, 5));
        bottom.add(btnSave);
        bottom.add(btnCancel);
        dlg.add(bottom, BorderLayout.SOUTH);

        dlg.pack();
        dlg.setLocationRelativeTo(this);
        dlg.setVisible(true);
    }

    private void showDetails(Commande c) {
        StringBuilder sb = new StringBuilder()
                .append("Commande #").append(c.getIdCommande()).append("\n")
                .append("Client : ").append(c.getClient().getNom()).append(" ").append(c.getClient().getPrenom()).append("\n")
                .append("Statut : ").append(c.getStatut()).append("\n")
                .append("Total : ").append(String.format("%.2f", c.getTotal())).append(" MAD");
        JOptionPane.showMessageDialog(this, sb.toString(), "Détails", JOptionPane.INFORMATION_MESSAGE);
    }

    private void invoice(Commande c) {
        try {
            // Marquer expédiée et mettre à jour stock
            c.setStatut("Expédiée");
            commandeDao.update(c);
            for (Cmd_prd_pivot l : c.getLignesCommande()) {
                Produit p = produitDao.findById(l.getProduit().getIdProduit());
                p.setQuantiteStock(p.getQuantiteStock() - l.getQuantite());
                produitDao.update(p);
            }
            loadCommandes();

            // Générer et sauvegarder PDF
            JFileChooser fc = new JFileChooser();
            fc.setSelectedFile(new java.io.File("facture_" + c.getIdCommande() + ".pdf"));
            if (fc.showSaveDialog(this) == JFileChooser.APPROVE_OPTION) {
                PDFGenerator.generateFacture(c, fc.getSelectedFile().getAbsolutePath());
                JOptionPane.showMessageDialog(this, "Facture générée !", "Succès", JOptionPane.INFORMATION_MESSAGE);
            }
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, "Erreur PDF : " + e.getMessage(), "Erreur", JOptionPane.ERROR_MESSAGE);
        }
    }
}
