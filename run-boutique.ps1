Write-Host "Starting Boutique Management Application..." -ForegroundColor Green
Write-Host ""
Write-Host "Make sure Laragon is running with MySQL started!" -ForegroundColor Yellow
Write-Host ""

# Create lib directory if it doesn't exist
if (!(Test-Path "lib")) {
    New-Item -ItemType Directory -Path "lib"
    Write-Host "Created lib directory" -ForegroundColor Blue
}

# Check if dependencies exist
if (!(Test-Path "lib\mysql-connector-java-8.0.33.jar")) {
    Write-Host "Missing MySQL Connector! Please download it manually." -ForegroundColor Red
    Write-Host "Download from: https://dev.mysql.com/downloads/connector/j/" -ForegroundColor Yellow
    Write-Host "Place mysql-connector-java-8.0.33.jar in the lib folder" -ForegroundColor Yellow
    Read-Host "Press Enter to continue after downloading the JAR file"
}

# Compile the application first
Write-Host "Compiling application..." -ForegroundColor Blue
javac -cp "lib\*" -d target\classes -sourcepath src\main\java src\main\java\com\boutique\ui\LoginFrame.java

if ($LASTEXITCODE -eq 0) {
    # Run the application with all dependencies
    Write-Host "Running application..." -ForegroundColor Green
    java -cp "target\classes;lib\*" com.boutique.ui.LoginFrame
} else {
    Write-Host "Compilation failed!" -ForegroundColor Red
}

Read-Host "Press Enter to exit"
