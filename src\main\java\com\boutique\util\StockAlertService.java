package com.boutique.util;

import com.boutique.dao.impl.ProduitDAOImpl;
import com.boutique.model.Produit;

import java.awt.*;
import java.awt.TrayIcon.MessageType;
import java.awt.image.BufferedImage;
import java.net.URL;
import java.util.List;

public class StockAlertService {
    private static TrayIcon trayIcon;
    private static int threshold=10;

    public static void initSystemTray() {
        if (!SystemTray.isSupported()) return;

        // 1) Load warning.png from classpath
        URL url = StockAlertService.class
                .getClassLoader()
                .getResource("icons/logo.png");
        Image image;
        if (url != null) {
            image = Toolkit.getDefaultToolkit().getImage(url);
        } else {
            System.err.println("⚠️ warning.png not found on classpath! Using blank icon.");
            image = new BufferedImage(16, 16, BufferedImage.TYPE_INT_ARGB);
        }

        // 2) Create and add the TrayIcon
        trayIcon = new TrayIcon(image, "Gestion de boutique");
        trayIcon.setImageAutoSize(true);
        try {
            SystemTray.getSystemTray().add(trayIcon);
        } catch (AWTException e) {
            e.printStackTrace();
        }
    }

    public static void setThreshold(int newThreshold) {
        threshold = newThreshold;
    }

    public static void checkStock() {
        if (trayIcon == null) return;
        List<Produit> produits = new ProduitDAOImpl().findAll();
        for (Produit p : produits) {
            if (p.getQuantiteStock() <= threshold) {
                trayIcon.displayMessage(
                        "Stock faible : " + p.getNom(), "Il reste seulement " + p.getQuantiteStock() + " unité(s).", MessageType.WARNING
                );

            }
        }
    }
}
